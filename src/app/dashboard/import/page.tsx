'use client'

import { useState } from 'react'

import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
// Tabs components removed - not used
import { 
  Upload, 
  FileText, 
  Database, 
  Globe, 
  ArrowRight, 
  CheckCircle, 
  Clock,
  Sparkles,
  FileSpreadsheet,
  Play
} from 'lucide-react'
import Link from 'next/link'

export default function ImportPage() {
  const [selectedMethod, setSelectedMethod] = useState<string | null>(null)

  const importMethods = [
    {
      id: 'file-upload',
      title: 'File Upload',
      description: 'Upload CSV, Excel, or other data files directly',
      icon: Upload,
      color: 'blue',
      features: ['CSV Files', 'Excel (.xlsx, .xls)', 'JSON Files', 'Up to 100MB'],
      status: 'available',
      href: '/dashboard/data/upload'
    },
    {
      id: 'google-sheets',
      title: 'Google Sheets',
      description: 'Connect and import data from Google Sheets',
      icon: FileSpreadsheet,
      color: 'green',
      features: ['Real-time sync', 'Multiple sheets', 'Auto-refresh', 'Collaborative'],
      status: 'available',
      href: '/dashboard/data/google-sheets'
    },
    {
      id: 'firebase-sync',
      title: 'Firebase Projects',
      description: 'Connect and sync data from other Firebase projects in real-time',
      icon: Database,
      color: 'purple',
      features: ['Real-time sync', 'Bidirectional', 'Multiple projects', 'Conflict resolution'],
      status: 'available',
      href: '/dashboard/data/firebase-sync'
    },
    {
      id: 'database',
      title: 'Database Connection',
      description: 'Connect to MySQL, PostgreSQL, MongoDB and more',
      icon: Database,
      color: 'purple',
      features: ['MySQL', 'PostgreSQL', 'MongoDB', 'SQL Server'],
      status: 'coming-soon',
      href: '#'
    },
    {
      id: 'api',
      title: 'API Integration',
      description: 'Import data from REST APIs and web services',
      icon: Globe,
      color: 'orange',
      features: ['REST APIs', 'GraphQL', 'Webhooks', 'Custom endpoints'],
      status: 'coming-soon',
      href: '#'
    }
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'available':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Available</Badge>
      case 'coming-soon':
        return <Badge variant="secondary">Coming Soon</Badge>
      default:
        return null
    }
  }



  return (
    <div className="min-h-screen bg-slate-50">
      <div className="container mx-auto max-w-7xl px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
          {/* Hero Section */}
          <div className="lg:col-span-12 relative overflow-hidden rounded-3xl bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 p-8 md:p-12 text-white">
            <div className="absolute inset-0 opacity-20">
              <div className="absolute inset-0" style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
              }}></div>
            </div>
            <div className="relative z-10 flex flex-col lg:flex-row items-center justify-between">
              <div className="max-w-2xl">
                <div className="flex items-center space-x-2 mb-4">
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-green-400 text-sm font-medium">Import Ready</span>
                  </div>
                </div>
                <h1 className="text-4xl md:text-5xl font-bold text-white mb-4 leading-tight">
                  Import Your Data,
                  <span className="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-purple-400">
                    Seamlessly
                  </span>
                </h1>
                <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                  Choose how you&apos;d like to bring your data into the platform.
                  We support multiple sources to fit your workflow.
                </p>
                <div className="flex flex-wrap items-center gap-6 text-sm text-gray-400">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-400" />
                    <span>Secure Processing</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Sparkles className="h-4 w-4 text-purple-400" />
                    <span>Auto-Detection</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-blue-400" />
                    <span>Real-time Preview</span>
                  </div>
                </div>
              </div>

              {/* Right side visual element */}
              <div className="hidden lg:block">
                <div className="relative">
                  <div className="relative w-64 h-64">
                    <div className="absolute inset-0 bg-gradient-to-br from-cyan-400/20 to-purple-400/20 rounded-full blur-3xl"></div>
                    <div className="absolute top-4 right-8 w-16 h-12 bg-white/15 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center animate-pulse">
                      <FileText className="w-6 h-6 text-white/80" />
                    </div>
                    <div className="absolute top-20 left-4 w-20 h-14 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center" style={{animationDelay: '0.5s'}}>
                      <Database className="w-7 h-7 text-cyan-300" />
                    </div>
                    <div className="absolute bottom-16 right-4 w-18 h-16 bg-white/20 backdrop-blur-sm rounded-2xl border border-white/30 flex items-center justify-center animate-pulse" style={{animationDelay: '1s'}}>
                      <Upload className="w-8 h-8 text-purple-300" />
                    </div>
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                      <div className="w-24 h-24 bg-gradient-to-br from-white/20 to-white/10 backdrop-blur-md rounded-3xl border border-white/30 flex items-center justify-center shadow-2xl">
                        <div className="relative">
                          <Sparkles className="w-10 h-10 text-white animate-pulse" />
                          <div className="absolute -top-1 -right-1 w-3 h-3 bg-cyan-400 rounded-full animate-ping"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Import Methods Grid */}
          <div className="lg:col-span-12 grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            {importMethods.map((method) => {
              const Icon = method.icon
              const isAvailable = method.status === 'available'

              return (
                <div
                  key={method.id}
                  className={`group bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-lg transition-all duration-300 cursor-pointer ${
                    selectedMethod === method.id ? 'ring-2 ring-purple-500 ring-offset-2' : ''
                  } ${!isAvailable ? 'opacity-75' : 'hover:-translate-y-1'}`}
                  onClick={() => isAvailable && setSelectedMethod(method.id)}
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-4">
                      <div className={`p-3 rounded-xl bg-gradient-to-br ${
                        method.color === 'blue' ? 'from-blue-500 to-cyan-600' :
                        method.color === 'green' ? 'from-emerald-500 to-teal-600' :
                        method.color === 'purple' ? 'from-purple-500 to-pink-600' :
                        'from-orange-500 to-red-600'
                      } group-hover:scale-110 transition-transform duration-300`}>
                        <Icon className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-1">
                          {method.title}
                        </h3>
                        <p className="text-sm text-gray-600">
                          {method.description}
                        </p>
                      </div>
                    </div>
                    {getStatusBadge(method.status)}
                  </div>

                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-2">
                      {method.features.map((feature, index) => (
                        <div key={index} className="flex items-center space-x-2 text-sm text-gray-700">
                          <CheckCircle className="h-3 w-3 text-green-600" />
                          <span>{feature}</span>
                        </div>
                      ))}
                    </div>

                    {isAvailable ? (
                      <Link href={method.href}>
                        <Button className="w-full rounded-xl bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300">
                          Get Started
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </Link>
                    ) : (
                      <Button disabled className="w-full rounded-xl bg-gray-100 text-gray-500 font-semibold">
                        <Clock className="mr-2 h-4 w-4" />
                        Coming Soon
                      </Button>
                    )}
                  </div>
                </div>
              )
            })}
          </div>

          {/* Quick Start Guide */}
          <div className="lg:col-span-12">
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
              <div className="flex items-center space-x-3 mb-6">
                <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl">
                  <Play className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900">Quick Start Guide</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    Get up and running in minutes
                  </p>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                    <span className="text-white font-bold">1</span>
                  </div>
                  <h4 className="font-semibold text-gray-900 mb-2">Choose Import Method</h4>
                  <p className="text-sm text-gray-600">Select how you want to import your data</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                    <span className="text-white font-bold">2</span>
                  </div>
                  <h4 className="font-semibold text-gray-900 mb-2">Upload & Preview</h4>
                  <p className="text-sm text-gray-600">Upload your data and preview the structure</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                    <span className="text-white font-bold">3</span>
                  </div>
                  <h4 className="font-semibold text-gray-900 mb-2">Create Reports</h4>
                  <p className="text-sm text-gray-600">Generate insights and professional reports</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
