'use client'

import { useState } from 'react'
import { FileUpload } from '@/components/data/file-upload'
import { DataPreview } from '@/components/data/data-preview'
import { Button } from '@/components/ui/button'

import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { parseCSV, detectDataTypes, validateData } from '@/lib/utils'
import * as XLSX from 'xlsx'
import { useAuth } from '@/components/auth/auth-provider'
import { dataSourcesService } from '@/lib/firestore'
import { useRouter } from 'next/navigation'

interface ProcessingStep {
  id: string
  name: string
  status: 'pending' | 'processing' | 'completed' | 'error'
  progress: number
}

export default function DataUploadPage() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'success' | 'error'>('idle')
  const [uploadError, setUploadError] = useState<string>('')
  const [parsedData, setParsedData] = useState<Record<string, unknown>[]>([])
  const [dataSchema, setDataSchema] = useState<Record<string, string>>({})
  const [validationResults, setValidationResults] = useState<{ errors: string[]; warnings: string[] } | null>(null)
  const [showRawData, setShowRawData] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [saveError, setSaveError] = useState<string>('')
  const [processingSteps, setProcessingSteps] = useState<ProcessingStep[]>([
    { id: 'parse', name: 'Parsing file', status: 'pending', progress: 0 },
    { id: 'validate', name: 'Validating data', status: 'pending', progress: 0 },
    { id: 'analyze', name: 'Analyzing structure', status: 'pending', progress: 0 },
    { id: 'complete', name: 'Processing complete', status: 'pending', progress: 0 }
  ])

  const { user } = useAuth()
  const router = useRouter()

  const updateStepStatus = (stepId: string, status: ProcessingStep['status'], progress: number = 0) => {
    setProcessingSteps(prev => prev.map(step => 
      step.id === stepId ? { ...step, status, progress } : step
    ))
  }

  const processFile = async (file: File) => {
    setUploadStatus('uploading')
    setUploadError('')
    
    try {
      // Step 1: Parse file
      updateStepStatus('parse', 'processing', 25)
      
      let data: Record<string, unknown>[] = []
      
      if (file.name.endsWith('.csv')) {
        const text = await file.text()
        const parsed = parseCSV(text)
        data = parsed.data
      } else if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
        const buffer = await file.arrayBuffer()
        const workbook = XLSX.read(buffer, { type: 'buffer' })
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]
        data = XLSX.utils.sheet_to_json(worksheet)
      }
      
      updateStepStatus('parse', 'completed', 100)
      
      // Step 2: Validate data
      updateStepStatus('validate', 'processing', 50)
      
      if (data.length === 0) {
        throw new Error('No data found in file')
      }
      
      updateStepStatus('validate', 'completed', 100)
      
      // Step 3: Analyze structure
      updateStepStatus('analyze', 'processing', 75)
      
      const schema = detectDataTypes(data)
      const validation = validateData(data, schema)
      
      updateStepStatus('analyze', 'completed', 100)
      updateStepStatus('complete', 'completed', 100)
      
      // Set results
      setParsedData(data)
      setDataSchema(schema)
      setValidationResults(validation)
      setUploadStatus('success')
      
    } catch (error) {
      console.error('Error processing file:', error)
      setUploadError(error instanceof Error ? error.message : 'Failed to process file')
      setUploadStatus('error')
      
      // Mark current step as error
      const currentStep = processingSteps.find(step => step.status === 'processing')
      if (currentStep) {
        updateStepStatus(currentStep.id, 'error', 0)
      }
    }
  }

  const handleFileSelect = (file: File) => {
    setSelectedFile(file)
    processFile(file)
  }

  const handleFileRemove = () => {
    setSelectedFile(null)
    setUploadStatus('idle')
    setUploadError('')
    setParsedData([])
    setDataSchema({})
    setValidationResults(null)
    setIsSaving(false)
    setSaveError('')
    setProcessingSteps(prev => prev.map(step => ({ ...step, status: 'pending', progress: 0 })))
  }

  const handleSaveDataset = async () => {
    if (!user || !selectedFile || parsedData.length === 0) return

    try {
      setIsSaving(true)
      setSaveError('')

      // Determine file type
      const fileExtension = selectedFile.name.split('.').pop()?.toLowerCase()
      let dataSourceType: 'csv' | 'google_sheets' | 'api' = 'csv'

      if (fileExtension === 'xlsx' || fileExtension === 'xls') {
        // For now, we'll treat Excel files as CSV since we don't have a separate type
        dataSourceType = 'csv'
      }

      // Create data source configuration
      const config = {
        file_name: selectedFile.name,
        file_size: selectedFile.size,
        file_type: fileExtension,
        schema: dataSchema,
        record_count: parsedData.length,
        upload_date: new Date().toISOString()
      }

      // Save to Firebase
      const _dataSourceId = await dataSourcesService.create({
        user_id: user.uid,
        name: selectedFile.name,
        type: dataSourceType,
        config: config,
        status: 'active',
        last_sync: new Date().toISOString()
      })

      // Redirect to create report page or show success message
      router.push('/dashboard/create-report')

    } catch (error) {
      console.error('Error saving dataset:', error)
      setSaveError(error instanceof Error ? error.message : 'Failed to save dataset')
    } finally {
      setIsSaving(false)
    }
  }

  const getStepIcon = (status: ProcessingStep['status']) => {
    switch (status) {
      case 'completed':
        return '✓'
      case 'processing':
        return '⟳'
      case 'error':
        return '✗'
      default:
        return '○'
    }
  }

  const getStepColor = (status: ProcessingStep['status']) => {
    switch (status) {
      case 'completed':
        return 'text-green-600'
      case 'processing':
        return 'text-blue-600'
      case 'error':
        return 'text-red-600'
      default:
        return 'text-gray-400'
    }
  }

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="container mx-auto max-w-7xl px-6 py-8">
        {/* Hero Section */}
        <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 p-8 mb-8">
          <div className="absolute inset-0 opacity-20">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}></div>
          </div>
          <div className="relative z-10">
            <div className="max-w-2xl">
              <div className="flex items-center space-x-2 mb-4">
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-400 text-sm font-medium">Upload Ready</span>
                </div>
              </div>
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-4 leading-tight">
                Upload Your Data,
                <span className="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-purple-400">
                  Start Creating
                </span>
              </h1>
              <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                Upload your CSV or Excel files to transform them into professional reports and analytics
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Upload Section */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">File Upload</h3>
                <p className="text-sm text-gray-600">
                  Select a CSV or Excel file to upload and process
                </p>
              </div>
              <FileUpload
                onFileSelect={handleFileSelect}
                onFileRemove={handleFileRemove}
                selectedFile={selectedFile}
                uploadStatus={uploadStatus}
                uploadError={uploadError}
              />
            </div>

            {/* Processing Steps */}
            {selectedFile && (
              <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 mt-6">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">Processing Status</h3>
                </div>
                <div className="space-y-4">
                  {processingSteps.map((step) => (
                    <div key={step.id} className="flex items-center space-x-3">
                      <span className={`text-lg ${getStepColor(step.status)}`}>
                        {getStepIcon(step.status)}
                      </span>
                      <div className="flex-1">
                        <div className="text-sm font-medium">{step.name}</div>
                        {step.status === 'processing' && (
                          <Progress value={step.progress} className="mt-1" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Preview Section */}
          <div className="lg:col-span-2">
            {parsedData.length > 0 && (
              <div className="bg-white rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
                <Tabs defaultValue="preview" className="w-full">
                  <div className="p-6 border-b border-gray-100">
                    <TabsList className="grid w-full grid-cols-2 bg-gray-50 rounded-xl p-1">
                      <TabsTrigger value="preview" className="rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-sm">
                        Data Preview
                      </TabsTrigger>
                      <TabsTrigger value="analysis" className="rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-sm">
                        Analysis
                      </TabsTrigger>
                    </TabsList>
                  </div>

                  <TabsContent value="preview" className="p-6 pt-0 mt-6">
                    <DataPreview
                      data={parsedData}
                      schema={dataSchema}
                      validationResults={validationResults ? {
                        ...validationResults,
                        isValid: validationResults.errors.length === 0,
                        stats: {
                          totalRows: parsedData.length,
                          totalColumns: Object.keys(dataSchema).length,
                          nullPercentage: '0%'
                        }
                      } : undefined}
                      showRawData={showRawData}
                      onToggleRawData={() => setShowRawData(!showRawData)}
                    />
                  </TabsContent>

                  <TabsContent value="analysis" className="p-6 pt-0 mt-6">
                    <div className="mb-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">Data Analysis</h3>
                      <p className="text-sm text-gray-600">
                        Insights and recommendations for your dataset
                      </p>
                    </div>
                    <div className="text-center py-12 text-gray-500 bg-gray-50 rounded-xl">
                      <div className="max-w-md mx-auto">
                        <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                          <span className="text-2xl">📊</span>
                        </div>
                        <p className="text-lg font-medium text-gray-700 mb-2">Advanced Analysis Coming Soon</p>
                        <p className="text-sm">
                          This will include data profiling, correlation analysis, and intelligent report recommendations
                        </p>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
            )}

            {parsedData.length === 0 && !selectedFile && (
              <div className="bg-white rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
                <div className="flex items-center justify-center py-20">
                  <div className="text-center text-gray-500 max-w-md">
                    <div className="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl mx-auto mb-6 flex items-center justify-center">
                      <span className="text-3xl">📁</span>
                    </div>
                    <p className="text-xl font-medium text-gray-700 mb-2">No data uploaded yet</p>
                    <p className="text-sm text-gray-500">Upload a CSV or Excel file to see the preview and analysis</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        {uploadStatus === 'success' && validationResults && validationResults.errors.length === 0 && (
          <div className="mt-8 bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-1">Ready to Proceed</h3>
                <p className="text-sm text-gray-600">Your data has been successfully processed and validated</p>
                {saveError && (
                  <p className="text-sm text-red-600 mt-2">{saveError}</p>
                )}
              </div>
              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  variant="outline"
                  onClick={handleSaveDataset}
                  disabled={isSaving}
                  className="rounded-xl border-gray-200 hover:border-gray-300 hover:bg-gray-50 transition-all duration-300"
                >
                  {isSaving ? 'Saving...' : 'Save Dataset'}
                </Button>
                <Button
                  onClick={handleSaveDataset}
                  disabled={isSaving}
                  className="rounded-xl bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  {isSaving ? 'Saving...' : 'Save & Create Report'}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
