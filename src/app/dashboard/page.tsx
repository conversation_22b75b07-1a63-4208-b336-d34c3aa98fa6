'use client'

import { useState, useEffect } from 'react'
// Card components removed - not used in this file
import { Button } from '@/components/ui/button'
import {
  BarChart3,
  Database,
  FileText,
  TrendingUp,
  Upload,
  Plus,
  Activity,
  FolderOpen,
  Download,

  ArrowRight,
  Zap,
  Sparkles,

  Eye,

  Calendar,
  Users,
  Globe,
  CheckCircle
} from 'lucide-react'
import Link from 'next/link'
import { useAuth } from '@/components/auth/auth-provider'
import { getUserDataSources, getUserReports } from '@/lib/firestore'
import { ReportModal } from '@/components/reports/report-modal'

export default function DashboardPage() {
  const [stats, setStats] = useState({
    totalRecords: 0,
    filesImported: 0,
    reportsCreated: 0,
    isLoading: true
  })

  const [recentReports, setRecentReports] = useState<any[]>([])
  const [isLoadingReports, setIsLoadingReports] = useState(false)
  const [selectedReportId, setSelectedReportId] = useState<string | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  const { user } = useAuth()

  // Load dashboard stats
  useEffect(() => {
    const loadStats = async () => {
      if (!user) return

      try {
        setIsLoadingReports(true)
        const [dataSources, reports] = await Promise.all([
          getUserDataSources(user.uid),
          getUserReports(user.uid)
        ])

        const totalRecords = dataSources.reduce((sum, source) => {
          return sum + ((source.metadata as any)?.record_count || 0)
        }, 0)

        setStats({
          totalRecords,
          filesImported: dataSources.length,
          reportsCreated: reports.length,
          isLoading: false
        })

        // Set recent reports (limit to 3 for dashboard)
        setRecentReports(reports.slice(0, 3))
      } catch (error) {
        console.error('Failed to load dashboard stats:', error)
        setStats(prev => ({ ...prev, isLoading: false }))
      } finally {
        setIsLoadingReports(false)
      }
    }

    loadStats()
  }, [user])

  const handleReportClick = (reportId: string) => {
    setSelectedReportId(reportId)
    setIsModalOpen(true)
  }

  const handleModalClose = () => {
    setSelectedReportId(null)
    setIsModalOpen(false)
  }
  return (
    <div className="min-h-screen bg-slate-50">
      <div className="container mx-auto max-w-7xl px-6 py-8">
        {/* Hero Section */}
        <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 p-8 mb-8">
          <div className="absolute inset-0 opacity-20">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}></div>
          </div>
          <div className="relative z-10 flex items-center justify-between">
            <div className="max-w-2xl">
              <div className="flex items-center space-x-2 mb-4">
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-400 text-sm font-medium">System Active</span>
                </div>
              </div>
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-4 leading-tight">
                Data to Insights,
                <span className="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-purple-400">
                  Instantly
                </span>
              </h1>
              <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                Transform your CSV files and Google Sheets into professional reports with our intelligent platform
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/dashboard/import">
                  <Button size="lg" className="bg-white text-gray-900 hover:bg-gray-100 font-semibold px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 w-full sm:w-auto">
                    <Upload className="mr-2 h-5 w-5" />
                    Start Importing
                  </Button>
                </Link>
                <Link href="/dashboard/create-report">
                  <Button size="lg" className="border-2 border-white/30 bg-white/10 text-white hover:bg-white/20 hover:text-white font-semibold px-8 py-4 rounded-2xl backdrop-blur-sm transition-all duration-300 hover:scale-105 w-full sm:w-auto hover:border-white/50">
                    <Sparkles className="mr-2 h-5 w-5" />
                    Create Report
                  </Button>
                </Link>
              </div>
            </div>
            <div className="hidden lg:block">
              <div className="relative">
                {/* Floating Elements */}
                <div className="relative w-64 h-64">
                  {/* Background glow */}
                  <div className="absolute inset-0 bg-gradient-to-br from-cyan-400/20 to-purple-400/20 rounded-full blur-3xl"></div>

                  {/* Floating cards */}
                  <div className="absolute top-4 right-8 w-16 h-12 bg-white/15 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center animate-pulse">
                    <FileText className="w-6 h-6 text-white/80" />
                  </div>

                  <div className="absolute top-20 left-4 w-20 h-14 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center" style={{animationDelay: '0.5s'}}>
                    <Database className="w-7 h-7 text-cyan-300" />
                  </div>

                  <div className="absolute bottom-16 right-4 w-18 h-16 bg-white/20 backdrop-blur-sm rounded-2xl border border-white/30 flex items-center justify-center animate-pulse" style={{animationDelay: '1s'}}>
                    <TrendingUp className="w-8 h-8 text-purple-300" />
                  </div>

                  {/* Central element */}
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    <div className="w-24 h-24 bg-gradient-to-br from-white/20 to-white/10 backdrop-blur-md rounded-3xl border border-white/30 flex items-center justify-center shadow-2xl">
                      <div className="relative">
                        <Sparkles className="w-10 h-10 text-white animate-pulse" />
                        <div className="absolute -top-1 -right-1 w-3 h-3 bg-cyan-400 rounded-full animate-ping"></div>
                      </div>
                    </div>
                  </div>

                  {/* Decorative dots */}
                  <div className="absolute top-8 left-12 w-2 h-2 bg-white/40 rounded-full animate-pulse"></div>
                  <div className="absolute bottom-8 left-8 w-1.5 h-1.5 bg-purple-300/60 rounded-full animate-pulse" style={{animationDelay: '0.7s'}}></div>
                  <div className="absolute top-16 right-16 w-1 h-1 bg-cyan-300/60 rounded-full animate-pulse" style={{animationDelay: '1.2s'}}></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Grid - Bento Box Style */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          {/* Large Featured Stat */}
          <div className="lg:col-span-2 bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
            <div className="flex items-start justify-between mb-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wide">Total Data Processed</h3>
                <div className="flex items-baseline space-x-2 mt-2">
                  <span className="text-4xl font-bold text-gray-900">
                    {stats.isLoading ? '...' : stats.totalRecords.toLocaleString()}
                  </span>
                  <span className="text-lg text-gray-500">records</span>
                </div>
              </div>
              <div className="p-3 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl">
                <Database className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center space-x-1 text-gray-500">
                <Activity className="h-4 w-4" />
                <span className="font-medium">Live Data</span>
              </div>
            </div>
          </div>

          {/* Compact Stats */}
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
            <div className="flex items-center justify-between mb-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FolderOpen className="h-5 w-5 text-blue-600" />
              </div>
              <CheckCircle className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <p className="text-2xl font-bold text-gray-900">
                {stats.isLoading ? '...' : stats.filesImported}
              </p>
              <p className="text-sm text-gray-500">Files Imported</p>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
            <div className="flex items-center justify-between mb-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <FileText className="h-5 w-5 text-purple-600" />
              </div>
              <CheckCircle className="h-4 w-4 text-purple-600" />
            </div>
            <div>
              <p className="text-2xl font-bold text-gray-900">
                {stats.isLoading ? '...' : stats.reportsCreated}
              </p>
              <p className="text-sm text-gray-500">Reports Created</p>
            </div>
          </div>

          {/* Activity Timeline */}
          <div className="lg:col-span-2 bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">Sales_Q4.csv processed successfully</p>
                  <p className="text-xs text-gray-500">2 minutes ago</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <Upload className="h-4 w-4 text-blue-600" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">New Google Sheet connected</p>
                  <p className="text-xs text-gray-500">15 minutes ago</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                  <Download className="h-4 w-4 text-purple-600" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">Monthly report exported</p>
                  <p className="text-xs text-gray-500">1 hour ago</p>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Quick Actions */}
          <div className="lg:col-span-2 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 rounded-2xl p-8 text-white relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10"></div>
            <div className="relative z-10">
              <div className="flex items-center space-x-3 mb-6">
                <div className="p-2 bg-white/10 rounded-lg backdrop-blur-sm">
                  <Zap className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold">Quick Actions</h3>
                  <p className="text-sm text-gray-300">Jump into your workflow</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <Link href="/dashboard/import">
                  <div className="group bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 rounded-xl p-4 transition-all duration-300 hover:scale-105 cursor-pointer backdrop-blur-sm">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-blue-500/20 rounded-lg group-hover:bg-blue-500/30 transition-colors">
                        <Upload className="h-5 w-5 text-blue-400" />
                      </div>
                      <div>
                        <p className="font-semibold text-white">Import Data</p>
                        <p className="text-xs text-gray-400">CSV & Google Sheets</p>
                      </div>
                    </div>
                  </div>
                </Link>

                <Link href="/dashboard/create-report">
                  <div className="group bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 rounded-xl p-4 transition-all duration-300 hover:scale-105 cursor-pointer backdrop-blur-sm">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-purple-500/20 rounded-lg group-hover:bg-purple-500/30 transition-colors">
                        <Plus className="h-5 w-5 text-purple-400" />
                      </div>
                      <div>
                        <p className="font-semibold text-white">Create Report</p>
                        <p className="text-xs text-gray-400">Build from data</p>
                      </div>
                    </div>
                  </div>
                </Link>

                <Link href="/dashboard/files">
                  <div className="group bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 rounded-xl p-4 transition-all duration-300 hover:scale-105 cursor-pointer backdrop-blur-sm">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-green-500/20 rounded-lg group-hover:bg-green-500/30 transition-colors">
                        <FolderOpen className="h-5 w-5 text-green-400" />
                      </div>
                      <div>
                        <p className="font-semibold text-white">Manage Files</p>
                        <p className="text-xs text-gray-400">View & organize</p>
                      </div>
                    </div>
                  </div>
                </Link>

                <Link href="/dashboard/export">
                  <div className="group bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 rounded-xl p-4 transition-all duration-300 hover:scale-105 cursor-pointer backdrop-blur-sm">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-orange-500/20 rounded-lg group-hover:bg-orange-500/30 transition-colors">
                        <Download className="h-5 w-5 text-orange-400" />
                      </div>
                      <div>
                        <p className="font-semibold text-white">Export Reports</p>
                        <p className="text-xs text-gray-400">PDF, Excel & more</p>
                      </div>
                    </div>
                  </div>
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Reports Section */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Your Reports</h2>
              <p className="text-gray-600 mt-1">Manage and view your generated reports</p>
            </div>
            <Link href="/dashboard/reports">
              <Button variant="outline" className="hover:bg-gray-50">
                View All
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>

          {/* Reports Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {isLoadingReports ? (
              // Loading skeleton
              Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                  <div className="flex items-start justify-between mb-4">
                    <div className="p-3 bg-gray-200 rounded-xl animate-pulse">
                      <div className="h-6 w-6 bg-gray-300 rounded"></div>
                    </div>
                    <div className="h-4 w-8 bg-gray-200 rounded animate-pulse"></div>
                  </div>
                  <div className="h-6 bg-gray-200 rounded mb-2 animate-pulse"></div>
                  <div className="h-4 bg-gray-200 rounded mb-4 animate-pulse"></div>
                  <div className="flex items-center justify-between">
                    <div className="h-4 w-20 bg-gray-200 rounded animate-pulse"></div>
                    <div className="h-4 w-16 bg-gray-200 rounded animate-pulse"></div>
                  </div>
                </div>
              ))
            ) : recentReports.length === 0 ? (
              <div className="col-span-full text-center py-12">
                <div className="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl mx-auto mb-6 flex items-center justify-center">
                  <FileText className="h-10 w-10 text-gray-400" />
                </div>
                <p className="text-xl font-medium text-gray-700 mb-2">No reports yet</p>
                <p className="text-sm text-gray-500 mb-6">Create your first report to see it here</p>
                <Link href="/dashboard/create-report">
                  <Button className="rounded-xl bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
                    <Plus className="mr-2 h-4 w-4" />
                    Create Report
                  </Button>
                </Link>
              </div>
            ) : (
              recentReports.map((report, index) => {
                const gradients = [
                  'from-blue-500 to-cyan-600',
                  'from-emerald-500 to-teal-600',
                  'from-purple-500 to-pink-600'
                ]
                const icons = [BarChart3, Users, Globe]
                const Icon = icons[index % icons.length]

                // Calculate time since creation
                const createdAt = new Date(report.created_at)
                const now = new Date()
                const diffInHours = Math.floor((now.getTime() - createdAt.getTime()) / (1000 * 60 * 60))

                let timeAgo: string
                if (diffInHours < 1) {
                  timeAgo = 'Just now'
                } else if (diffInHours < 24) {
                  timeAgo = `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`
                } else {
                  const diffInDays = Math.floor(diffInHours / 24)
                  timeAgo = `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`
                }

                return (
                  <div
                    key={report.id}
                    className="group bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 cursor-pointer"
                    onClick={() => handleReportClick(report.id)}
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className={`p-3 bg-gradient-to-br ${gradients[index % gradients.length]} rounded-xl group-hover:scale-110 transition-transform duration-300`}>
                        <Icon className="h-6 w-6 text-white" />
                      </div>
                      <div className="flex items-center space-x-1">
                        <CheckCircle className="h-4 w-4 text-green-400" />
                        <span className="text-sm text-gray-500">{report.status}</span>
                      </div>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{report.name}</h3>
                    <p className="text-sm text-gray-600 mb-4">{report.description || 'No description available'}</p>
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center space-x-2 text-gray-500">
                        <Calendar className="h-4 w-4" />
                        <span>{timeAgo}</span>
                      </div>
                      <div className="flex items-center space-x-1 text-blue-600 font-medium">
                        <Eye className="h-4 w-4" />
                        <span>View</span>
                      </div>
                    </div>
                  </div>
                )
              })
            )}
          </div>
        </div>
      </div>

      {/* Report Modal */}
      <ReportModal
        reportId={selectedReportId}
        isOpen={isModalOpen}
        onClose={handleModalClose}
      />
    </div>
  )
}
