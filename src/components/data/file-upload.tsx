'use client'

import { useCallback, useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { Upload, File, X, AlertCircle, CheckCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { formatBytes } from '@/lib/utils'

interface FileUploadProps {
  onFileSelect: (file: File) => void
  onFileRemove: () => void
  acceptedFileTypes?: string[]
  maxFileSize?: number
  selectedFile?: File | null
  uploadStatus?: 'idle' | 'uploading' | 'success' | 'error'
  uploadError?: string
}

export function FileUpload({
  onFileSelect,
  onFileRemove,
  acceptedFileTypes = ['csv', 'xlsx', 'xls'],
  maxFileSize = 10 * 1024 * 1024, // 10MB
  selectedFile,
  uploadStatus = 'idle',
  uploadError
}: FileUploadProps) {
  const [dragActive, setDragActive] = useState(false)

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    setDragActive(false)
    
    if (rejectedFiles.length > 0) {
      return
    }
    
    if (acceptedFiles.length > 0) {
      onFileSelect(acceptedFiles[0])
    }
  }, [onFileSelect])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls']
    },
    maxSize: maxFileSize,
    multiple: false,
    onDragEnter: () => setDragActive(true),
    onDragLeave: () => setDragActive(false)
  })

  const getStatusIcon = () => {
    switch (uploadStatus) {
      case 'uploading':
        return <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600" />
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-600" />
      default:
        return <File className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusText = () => {
    switch (uploadStatus) {
      case 'uploading':
        return 'Uploading...'
      case 'success':
        return 'Upload successful'
      case 'error':
        return 'Upload failed'
      default:
        return ''
    }
  }

  if (selectedFile) {
    return (
      <Card className="w-full">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {getStatusIcon()}
              <div>
                <p className="text-sm font-medium text-gray-900">{selectedFile.name}</p>
                <p className="text-xs text-gray-500">
                  {formatBytes(selectedFile.size)} • {getStatusText()}
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onFileRemove}
              disabled={uploadStatus === 'uploading'}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          
          {uploadError && (
            <Alert variant="destructive" className="mt-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{uploadError}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="w-full">
      <div
        {...getRootProps()}
        className={`
          relative border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
          ${isDragActive || dragActive 
            ? 'border-blue-400 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
          }
        `}
      >
        <input {...getInputProps()} />
        <Upload className={`mx-auto h-12 w-12 ${isDragActive || dragActive ? 'text-blue-500' : 'text-gray-400'}`} />
        <h3 className="mt-4 text-lg font-medium text-gray-900">
          {isDragActive ? 'Drop your file here' : 'Upload your data file'}
        </h3>
        <p className="mt-2 text-sm text-gray-500">
          Drag and drop your file here, or click to browse
        </p>
        <p className="mt-1 text-xs text-gray-400">
          Supports: {acceptedFileTypes.map(type => type.toUpperCase()).join(', ')} • Max size: {formatBytes(maxFileSize)}
        </p>
      </div>
      
      <div className="mt-4 text-center">
        <Button variant="outline" onClick={() => (document.querySelector('input[type="file"]') as HTMLInputElement)?.click()}>
          Choose File
        </Button>
      </div>
    </div>
  )
}
