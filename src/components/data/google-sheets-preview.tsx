'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  FileSpreadsheet, 
  Loader2, 
  CheckCircle, 
  AlertCircle,
  RefreshCw,
  ExternalLink,
  Download,
  Eye,
  EyeOff
} from 'lucide-react'
import { googleSheetsService } from '@/lib/google-sheets'

interface GoogleSheet {
  id: string
  name: string
  url: string
  lastModified: string
  sheets: Array<{
    id: number
    title: string
    rowCount: number
    columnCount: number
  }>
}

interface GoogleSheetsPreviewProps {
  sheet: GoogleSheet
  selectedSheetTab: string
  onDataLoad: (data: Record<string, any>[], schema: Record<string, string>) => void
  onError: (error: string) => void
  onSheetTabChange?: (tabName: string) => void
}

export function GoogleSheetsPreview({
  sheet,
  selectedSheetTab,
  onDataLoad,
  onError,
  onSheetTabChange
}: GoogleSheetsPreviewProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [previewData, setPreviewData] = useState<Record<string, any>[]>([])
  const [dataSchema, setDataSchema] = useState<Record<string, string>>({})
  const [showRawData, setShowRawData] = useState(false)
  const [totalRows, setTotalRows] = useState(0)
  const [lastSync, setLastSync] = useState<string | null>(null)

  useEffect(() => {
    if (sheet && selectedSheetTab) {
      loadSheetData()
    }
  }, [sheet, selectedSheetTab])

  const loadSheetData = async () => {
    try {
      setIsLoading(true)

      const sheetData = await googleSheetsService.getSheetData(sheet.id, selectedSheetTab)

      setPreviewData(sheetData.data.slice(0, 10)) // Preview only first 10 rows
      setDataSchema(sheetData.schema)
      setTotalRows(sheetData.totalRows)
      setLastSync(new Date().toISOString())

      // Notify parent component
      onDataLoad(sheetData.data, sheetData.schema)

    } catch (error) {
      console.error('Error loading sheet data:', error)
      onError('Failed to load sheet data. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }



  const getDataTypeColor = (type: string) => {
    switch (type) {
      case 'string': return 'bg-blue-100 text-blue-800'
      case 'number': return 'bg-green-100 text-green-800'
      case 'date': return 'bg-purple-100 text-purple-800'
      case 'boolean': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (isLoading) {
    return (
      <div className="p-8">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Sheet Data</h3>
          <p className="text-sm text-gray-600">
            Fetching data from &quot;{selectedSheetTab}&quot; in &quot;{sheet.name}&quot;...
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Sheet Info */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <FileSpreadsheet className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">{sheet.name}</h3>
              <p className="text-sm text-gray-600 mt-1">
                Sheet: {selectedSheetTab} • {totalRows} rows • {Object.keys(dataSchema).length} columns
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open(sheet.url, '_blank')}
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              Open in Google Sheets
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={loadSheetData}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            <div className="flex items-center space-x-1">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span>Connected</span>
            </div>
            {lastSync && (
              <div>
                Last synced: {new Date(lastSync).toLocaleTimeString()}
              </div>
            )}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowRawData(!showRawData)}
          >
            {showRawData ? (
              <>
                <EyeOff className="h-4 w-4 mr-2" />
                Hide Raw Data
              </>
            ) : (
              <>
                <Eye className="h-4 w-4 mr-2" />
                Show Raw Data
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Sheet Tab Selection */}
      {sheet.sheets.length > 1 && onSheetTabChange && (
        <div>
          <h4 className="font-semibold text-gray-900 mb-3">Sheet Tabs</h4>
          <div className="flex flex-wrap gap-2">
            {sheet.sheets.map((tab) => (
              <Button
                key={tab.id}
                variant={selectedSheetTab === tab.title ? 'default' : 'outline'}
                size="sm"
                onClick={() => onSheetTabChange(tab.title)}
                className="text-xs"
              >
                <FileSpreadsheet className="mr-1 h-3 w-3" />
                <div className="text-left">
                  <div className="font-medium">{tab.title}</div>
                  <div className="text-xs opacity-70">{tab.rowCount} rows</div>
                </div>
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Data Schema */}
      <div>
        <h4 className="font-semibold text-gray-900 mb-3">Data Schema</h4>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
          {Object.entries(dataSchema).map(([column, type]) => (
            <div key={column} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">{column}</p>
              </div>
              <Badge className={`ml-2 text-xs ${getDataTypeColor(type)}`}>
                {type}
              </Badge>
            </div>
          ))}
        </div>


      </div>

      {/* Data Preview */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h4 className="font-semibold text-gray-900">Data Preview</h4>
          <Badge variant="secondary">
            Showing 10 of {totalRows} rows
          </Badge>
        </div>
        <div>
          {showRawData ? (
            previewData.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b border-gray-200">
                      {Object.keys(dataSchema).map((column) => (
                        <th key={column} className="text-left p-3 font-medium text-gray-900 bg-gray-50">
                          {column}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {previewData.map((row, index) => (
                      <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                        {Object.keys(dataSchema).map((column) => (
                          <td key={column} className="p-3 text-gray-700">
                            {String(row[column] || '')}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8">
                <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-sm text-gray-600">No data available in this sheet</p>
              </div>
            )
          ) : (
            <div className="text-center py-12">
              <FileSpreadsheet className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <p className="text-lg font-medium text-gray-600 mb-2">Data Preview Hidden</p>
              <p className="text-sm text-gray-500">Click &quot;Show Raw Data&quot; to view the spreadsheet content</p>
            </div>
          )}
        </div>
      </div>

      {/* Import Actions */}
      <div className="flex justify-between items-center">
        <Alert className="flex-1 mr-4">
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            Data is ready to import. This will create a new data source in your account.
          </AlertDescription>
        </Alert>
        <Button
          className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700"
          onClick={() => onDataLoad(previewData, dataSchema)}
        >
          <Download className="mr-2 h-4 w-4" />
          Import Data
        </Button>
      </div>
    </div>
  )
}
