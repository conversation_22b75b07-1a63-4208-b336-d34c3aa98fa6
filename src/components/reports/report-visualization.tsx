'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Bar<PERSON>hart3, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  TrendingUp, 
  Activity,
  Loader2,
  AlertCircle,
  Download,
  RefreshCw,
  Eye,
  EyeOff
} from 'lucide-react'
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  <PERSON><PERSON>hart as RechartsLineChart,
  Line,
  Pie<PERSON>hart as RechartsPieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts'
import { googleSheetsService } from '@/lib/google-sheets'
import { dataSourcesService } from '@/lib/firestore'
import { analyzeData, prepareChartData, DataAnalysis, ChartRecommendation } from '@/lib/data-analysis'

interface ReportVisualizationProps {
  report: any
  dataSource: any
}

interface ChartData {
  [key: string]: any
}

const COLORS = ['#8b5cf6', '#06b6d4', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4']

export function ReportVisualization({ report, dataSource }: ReportVisualizationProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [rawData, setRawData] = useState<ChartData[]>([])
  const [dataSchema, setDataSchema] = useState<Record<string, string>>({})
  const [analysis, setAnalysis] = useState<DataAnalysis | null>(null)
  const [showRawData, setShowRawData] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')
  const [chartsReady, setChartsReady] = useState(false)

  useEffect(() => {
    if (report && dataSource) {
      loadReportData()
    }
  }, [report, dataSource])

  const loadReportData = async () => {
    try {
      setIsLoading(true)
      setError('')
      setChartsReady(false)

      // Get the data source configuration
      const dataSourceConfig = dataSource.config as any
      
      if (dataSource.type === 'google_sheets') {
        // Fetch data from Google Sheets
        const sheetData = await googleSheetsService.getSheetData(
          dataSourceConfig.sheet_id,
          dataSourceConfig.sheet_tab
        )
        
        setRawData(sheetData.data)
        setDataSchema(sheetData.schema)
        
        // Analyze the data using enhanced analysis
        const dataAnalysis = analyzeData(sheetData.data, sheetData.schema)
        setAnalysis(dataAnalysis)

        // Set charts ready after a small delay to ensure DOM is ready
        setTimeout(() => setChartsReady(true), 100)
      }
    } catch (error) {
      console.error('Error loading report data:', error)
      setError('Failed to load report data. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }



  const getChartData = (type: string, recommendation?: ChartRecommendation) => {
    if (!rawData.length || !analysis) return []

    if (recommendation) {
      return prepareChartData(rawData, type, recommendation.xColumn, recommendation.yColumn)
    }

    // Fallback to first available columns
    const { numericColumns, categoricalColumns } = analysis.summary

    switch (type) {
      case 'bar':
      case 'line':
      case 'area':
        return prepareChartData(rawData, type, categoricalColumns[0], numericColumns[0])
      case 'pie':
        return prepareChartData(rawData, type, categoricalColumns[0])
      default:
        return []
    }
  }

  // Helper function to render chart components safely
  const renderChartComponent = (type: string, data: any[], size: 'small' | 'large' = 'small') => {
    if (!data || data.length === 0) return null

    const pieRadius = size === 'large' ? 120 : 60

    switch (type) {
      case 'bar':
        return (
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="value" fill="#8b5cf6" />
          </BarChart>
        )

      case 'line':
        return (
          <RechartsLineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line type="monotone" dataKey="value" stroke="#8b5cf6" strokeWidth={2} />
          </RechartsLineChart>
        )

      case 'pie':
        return (
          <RechartsPieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              outerRadius={pieRadius}
              fill="#8884d8"
              dataKey="value"
              label={size === 'large' ? ({ name, percent }) => `${name}: ${((percent || 0) * 100).toFixed(1)}%` : ({ name, percent }) => `${name} ${((percent || 0) * 100).toFixed(0)}%`}
              labelLine={size === 'large' ? false : undefined}
            >
              {data.map((entry, idx) => (
                <Cell key={`cell-${idx}`} fill={COLORS[idx % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip />
            {size === 'large' && <Legend />}
          </RechartsPieChart>
        )

      case 'area':
        return (
          <AreaChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Area type="monotone" dataKey="value" stroke="#8b5cf6" fill="#8b5cf6" fillOpacity={0.3} />
          </AreaChart>
        )

      default:
        return null
    }
  }

  // Helper function to render chart safely
  const renderSafeChart = (chartType: string, data: any[], size: 'small' | 'large', height = 200) => {
    const chartComponent = renderChartComponent(chartType, data, size)
    if (!chartComponent) {
      return (
        <div className="text-center py-4 text-gray-500">
          <p className="text-sm">Chart not available</p>
        </div>
      )
    }
    return (
      <SafeChart data={data} height={height}>
        {chartComponent}
      </SafeChart>
    )
  }

  // Safe chart renderer component
  const SafeChart = ({ children, data, height = 200 }: { children: React.ReactElement, data: any[], height?: number }) => {
    // Check if charts are ready
    if (!chartsReady) {
      return (
        <div className={`flex items-center justify-center text-gray-500`} style={{ height }}>
          <div className="text-center">
            <Loader2 className="h-8 w-8 mx-auto mb-2 text-gray-400 animate-spin" />
            <p className="text-sm">Loading chart...</p>
          </div>
        </div>
      )
    }

    // Check if data is available
    if (!data || data.length === 0) {
      return (
        <div className={`flex items-center justify-center text-gray-500`} style={{ height }}>
          <div className="text-center">
            <BarChart3 className="h-8 w-8 mx-auto mb-2 text-gray-400" />
            <p className="text-sm">No data available</p>
          </div>
        </div>
      )
    }

    // Check if children is valid
    if (!children) {
      return (
        <div className={`flex items-center justify-center text-gray-500`} style={{ height }}>
          <div className="text-center">
            <AlertCircle className="h-8 w-8 mx-auto mb-2 text-orange-400" />
            <p className="text-sm">Chart type not supported</p>
          </div>
        </div>
      )
    }

    try {
      return (
        <ResponsiveContainer width="100%" height={height}>
          {children}
        </ResponsiveContainer>
      )
    } catch (error) {
      console.error('Chart rendering error:', error)
      return (
        <div className={`flex items-center justify-center text-gray-500`} style={{ height }}>
          <div className="text-center">
            <AlertCircle className="h-8 w-8 mx-auto mb-2 text-red-400" />
            <p className="text-sm">Chart rendering error</p>
            <p className="text-xs text-gray-400 mt-1">Check console for details</p>
          </div>
        </div>
      )
    }
  }

  const handleExportData = () => {
    if (!rawData.length) return

    // Convert data to CSV
    const headers = Object.keys(dataSchema)
    const csvContent = [
      headers.join(','),
      ...rawData.map(row =>
        headers.map(header => {
          const value = row[header]
          // Escape commas and quotes in CSV
          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            return `"${value.replace(/"/g, '""')}"`
          }
          return value || ''
        }).join(',')
      )
    ].join('\n')

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `${report.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_data.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-purple-600 mx-auto mb-4" />
            <p className="text-gray-600">Loading report data...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="py-12">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <div className="text-center mt-4">
            <Button onClick={loadReportData} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!analysis || rawData.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <AlertCircle className="h-8 w-8 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">No data available for visualization</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Data Summary */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Data Overview</CardTitle>
              <CardDescription>Summary of your imported data</CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowRawData(!showRawData)}
              >
                {showRawData ? <EyeOff className="h-4 w-4 mr-2" /> : <Eye className="h-4 w-4 mr-2" />}
                {showRawData ? 'Hide' : 'Show'} Raw Data
              </Button>
              <Button variant="outline" size="sm" onClick={loadReportData}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button variant="outline" size="sm" onClick={handleExportData}>
                <Download className="h-4 w-4 mr-2" />
                Export CSV
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600">Total Records</p>
                  <p className="text-2xl font-bold text-blue-900">{analysis.summary.totalRows.toLocaleString()}</p>
                </div>
                <Activity className="h-8 w-8 text-blue-500" />
              </div>
            </div>
            
            <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-600">Data Columns</p>
                  <p className="text-2xl font-bold text-green-900">{analysis.summary.totalColumns}</p>
                </div>
                <BarChart3 className="h-8 w-8 text-green-500" />
              </div>
            </div>
            
            <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-purple-600">Data Quality</p>
                  <p className="text-2xl font-bold text-purple-900">
                    {analysis.summary.nullPercentage < 5 ? '✅' : analysis.summary.nullPercentage < 15 ? '⚠️' : '❌'}
                  </p>
                  <p className="text-xs text-purple-600">
                    {(100 - analysis.summary.nullPercentage).toFixed(1)}% complete
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-purple-500" />
              </div>
            </div>
          </div>

          {/* Data Insights */}
          <div className="mb-6">
            <h4 className="font-medium text-gray-900 mb-3">Data Insights</h4>
            <div className="space-y-2">
              {analysis.insights.map((insight, index) => (
                <div key={index} className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0" />
                  <p className="text-sm text-gray-600">{insight}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Chart Recommendations */}
          <div className="mb-6">
            <h4 className="font-medium text-gray-900 mb-3">Recommended Visualizations</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {analysis.recommendedCharts.slice(0, 4).map((recommendation, index) => (
                <div key={index} className="bg-white border rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="font-medium text-sm text-gray-900">{recommendation.title}</h5>
                    <Badge variant="outline" className="text-xs">
                      {recommendation.type}
                    </Badge>
                  </div>
                  <p className="text-xs text-gray-600 mb-2">{recommendation.description}</p>
                  <p className="text-xs text-purple-600">{recommendation.reason}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Column Types */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <h5 className="font-medium text-gray-700 mb-2">Numeric Columns</h5>
              <div className="space-y-1">
                {analysis.summary.numericColumns.map(col => (
                  <Badge key={col} variant="secondary" className="mr-1 mb-1">{col}</Badge>
                ))}
              </div>
            </div>
            
            <div>
              <h5 className="font-medium text-gray-700 mb-2">Categorical Columns</h5>
              <div className="space-y-1">
                {analysis.summary.categoricalColumns.map(col => (
                  <Badge key={col} variant="outline" className="mr-1 mb-1">{col}</Badge>
                ))}
              </div>
            </div>
            
            <div>
              <h5 className="font-medium text-gray-700 mb-2">Date Columns</h5>
              <div className="space-y-1">
                {analysis.summary.dateColumns.map(col => (
                  <Badge key={col} variant="default" className="mr-1 mb-1">{col}</Badge>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Chart Visualizations */}
      <Card>
        <CardHeader>
          <CardTitle>Data Visualizations</CardTitle>
          <CardDescription>Interactive charts based on your data</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="bar">Bar Chart</TabsTrigger>
              <TabsTrigger value="line">Line Chart</TabsTrigger>
              <TabsTrigger value="pie">Pie Chart</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="mt-6">
              {analysis.recommendedCharts.length > 0 ? (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Render top 2 recommended charts */}
                  {analysis.recommendedCharts.slice(0, 2).map((recommendation, index) => {
                  const chartData = getChartData(recommendation.type, recommendation)
                  const IconComponent = recommendation.type === 'bar' ? BarChart3 :
                                       recommendation.type === 'pie' ? PieChart :
                                       recommendation.type === 'line' ? LineChart : TrendingUp

                  return (
                    <div key={index} className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                        <IconComponent className="h-5 w-5 mr-2 text-purple-600" />
                        {recommendation.title}
                      </h4>
                      <p className="text-sm text-gray-600 mb-4">{recommendation.description}</p>
                      {renderSafeChart(recommendation.type, chartData, 'small', 200)}
                    </div>
                    )
                  })}
                </div>
              ) : (
                <div className="flex items-center justify-center py-12 text-gray-500">
                  <div className="text-center">
                    <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                    <p className="text-lg font-medium mb-2">No Charts Available</p>
                    <p className="text-sm">Your data doesn't contain suitable columns for visualization</p>
                    <p className="text-xs text-gray-400 mt-2">Try importing data with numeric or categorical columns</p>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="bar" className="mt-6">
              <div className="bg-gray-50 rounded-lg p-6">
                {renderSafeChart('bar', getChartData('bar'), 'large', 400)}
              </div>
            </TabsContent>

            <TabsContent value="line" className="mt-6">
              <div className="bg-gray-50 rounded-lg p-6">
                {renderSafeChart('line', getChartData('line'), 'large', 400)}
              </div>
            </TabsContent>

            <TabsContent value="pie" className="mt-6">
              <div className="bg-gray-50 rounded-lg p-6 flex justify-center">
                {renderSafeChart('pie', getChartData('pie'), 'large', 400)}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Raw Data Table */}
      {showRawData && (
        <Card>
          <CardHeader>
            <CardTitle>Raw Data</CardTitle>
            <CardDescription>First 100 rows of your imported data</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-auto max-h-96 border rounded-lg">
              <table className="w-full text-sm">
                <thead className="bg-gray-50 sticky top-0">
                  <tr>
                    {Object.keys(dataSchema).map(column => (
                      <th key={column} className="px-4 py-2 text-left font-medium text-gray-700 border-b">
                        {column}
                        <Badge variant="outline" className="ml-2 text-xs">
                          {dataSchema[column]}
                        </Badge>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {rawData.slice(0, 100).map((row, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      {Object.keys(dataSchema).map(column => (
                        <td key={column} className="px-4 py-2 border-b text-gray-600">
                          {row[column]?.toString() || '-'}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
