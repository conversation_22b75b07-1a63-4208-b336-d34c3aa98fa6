'use client'

import { useState } from 'react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { User } from 'lucide-react'
import { cn } from '@/lib/utils'

interface UserAvatarProps {
  user?: {
    photoURL?: string | null
    displayName?: string | null
    email?: string | null
  } | null
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

export function UserAvatar({ user, className, size = 'md' }: UserAvatarProps) {
  const [imageError, setImageError] = useState(false)

  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12'
  }

  const iconSizes = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  }

  const handleImageError = () => {
    setImageError(true)
  }

  const getInitials = () => {
    if (user?.displayName) {
      return user.displayName
        .split(' ')
        .map(n => n[0])
        .join('')
        .toUpperCase()
        .slice(0, 2)
    }
    if (user?.email) {
      return user.email.charAt(0).toUpperCase()
    }
    return ''
  }

  // Don't show image if there was an error or no photoURL
  const shouldShowImage = user?.photoURL && !imageError

  return (
    <Avatar className={cn(sizeClasses[size], className)}>
      {shouldShowImage && (
        <AvatarImage
          src={user.photoURL || undefined}
          alt={user?.displayName || user?.email || 'User'}
          onError={handleImageError}
          // Add referrerPolicy to help with Google images
          referrerPolicy="no-referrer"
        />
      )}
      <AvatarFallback className="bg-gradient-to-br from-purple-500 to-blue-500 text-white font-medium">
        {getInitials() || <User className={iconSizes[size]} />}
      </AvatarFallback>
    </Avatar>
  )
}
