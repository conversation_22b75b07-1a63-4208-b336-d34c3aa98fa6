import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  DocumentSnapshot,
  QueryConstraint,
  serverTimestamp,
  Timestamp,
} from 'firebase/firestore'
import { db } from './firebase'
import {
  COLLECTIONS,
  User,
  CreateUser,
  UpdateUser,
  DataSource,
  CreateDataSource,
  UpdateDataSource,
  Dataset,
  CreateDataset,
  UpdateDataset,
  Report,
  CreateReport,
  UpdateReport,
  ReportRecommendation,
  CreateReportRecommendation,
  AuditLog,
  CreateAuditLog,
  ExternalFirebaseConnection,
  CreateExternalFirebaseConnection,
  UpdateExternalFirebaseConnection,
  SyncLog,
  CreateSyncLog,
} from './database.types'

// Helper function to convert Firestore timestamp to ISO string
const timestampToString = (timestamp: any): string => {
  if (timestamp instanceof Timestamp) {
    return timestamp.toDate().toISOString()
  }
  if (timestamp && typeof timestamp.toDate === 'function') {
    return timestamp.toDate().toISOString()
  }
  return timestamp || new Date().toISOString()
}

// Helper function to prepare data for Firestore (convert dates)
const prepareDataForFirestore = (data: any) => {
  const prepared = { ...data }
  const now = serverTimestamp()
  
  if (!prepared.created_at) {
    prepared.created_at = now
  }
  prepared.updated_at = now
  
  return prepared
}

// Helper function to convert Firestore document to typed object
const convertFirestoreDoc = <T>(doc: DocumentSnapshot): T | null => {
  if (!doc.exists()) return null
  
  const data = doc.data()
  if (!data) return null
  
  // Convert timestamps to ISO strings
  const converted = { ...data }
  if (converted.created_at) {
    converted.created_at = timestampToString(converted.created_at)
  }
  if (converted.updated_at) {
    converted.updated_at = timestampToString(converted.updated_at)
  }
  if (converted.last_sync) {
    converted.last_sync = timestampToString(converted.last_sync)
  }
  
  return { id: doc.id, ...converted } as T
}

// Generic CRUD operations
export class FirestoreService<T, CreateT, UpdateT> {
  constructor(private collectionName: string) {}

  async create(data: CreateT): Promise<string> {
    const preparedData = prepareDataForFirestore(data)
    const docRef = await addDoc(collection(db, this.collectionName), preparedData)
    return docRef.id
  }

  // Special method for creating documents with specific IDs (like user documents)
  async createWithId(id: string, data: CreateT): Promise<void> {
    const preparedData = prepareDataForFirestore(data)
    const docRef = doc(db, this.collectionName, id)
    await setDoc(docRef, preparedData)
  }

  async getById(id: string): Promise<T | null> {
    const docRef = doc(db, this.collectionName, id)
    const docSnap = await getDoc(docRef)
    return convertFirestoreDoc<T>(docSnap)
  }

  async update(id: string, data: UpdateT): Promise<void> {
    const docRef = doc(db, this.collectionName, id)
    const preparedData = { ...data, updated_at: serverTimestamp() }
    await updateDoc(docRef, preparedData)
  }

  async delete(id: string): Promise<void> {
    const docRef = doc(db, this.collectionName, id)
    await deleteDoc(docRef)
  }

  async getAll(constraints: QueryConstraint[] = []): Promise<T[]> {
    const q = query(collection(db, this.collectionName), ...constraints)
    const querySnapshot = await getDocs(q)
    return querySnapshot.docs.map(doc => convertFirestoreDoc<T>(doc)).filter(Boolean) as T[]
  }

  async getByField(field: string, value: any, constraints: QueryConstraint[] = []): Promise<T[]> {
    const q = query(
      collection(db, this.collectionName),
      where(field, '==', value),
      ...constraints
    )
    const querySnapshot = await getDocs(q)
    return querySnapshot.docs.map(doc => convertFirestoreDoc<T>(doc)).filter(Boolean) as T[]
  }
}

// Specialized Users Service that uses document ID matching user UID
class UsersService extends FirestoreService<User, CreateUser, UpdateUser> {
  constructor() {
    super(COLLECTIONS.USERS)
  }

  // Override create method to use user ID as document ID
  async create(data: CreateUser): Promise<string> {
    if (!data.id) {
      throw new Error('User ID is required for user document creation')
    }
    await this.createWithId(data.id, data)
    return data.id
  }
}

// Service instances for each collection
export const usersService = new UsersService()
export const dataSourcesService = new FirestoreService<DataSource, CreateDataSource, UpdateDataSource>(COLLECTIONS.DATA_SOURCES)
export const datasetsService = new FirestoreService<Dataset, CreateDataset, UpdateDataset>(COLLECTIONS.DATASETS)
export const reportsService = new FirestoreService<Report, CreateReport, UpdateReport>(COLLECTIONS.REPORTS)
export const reportRecommendationsService = new FirestoreService<ReportRecommendation, CreateReportRecommendation, Partial<ReportRecommendation>>(COLLECTIONS.REPORT_RECOMMENDATIONS)
export const auditLogsService = new FirestoreService<AuditLog, CreateAuditLog, Partial<AuditLog>>(COLLECTIONS.AUDIT_LOGS)
export const externalFirebaseConnectionsService = new FirestoreService<ExternalFirebaseConnection, CreateExternalFirebaseConnection, UpdateExternalFirebaseConnection>(COLLECTIONS.EXTERNAL_FIREBASE_CONNECTIONS)
export const syncLogsService = new FirestoreService<SyncLog, CreateSyncLog, Partial<SyncLog>>(COLLECTIONS.SYNC_LOGS)

// Specialized query functions
export const getUserDataSources = async (userId: string): Promise<DataSource[]> => {
  return dataSourcesService.getByField('user_id', userId, [orderBy('created_at', 'desc')])
}

export const getDataSourceDatasets = async (dataSourceId: string): Promise<Dataset[]> => {
  return datasetsService.getByField('data_source_id', dataSourceId, [orderBy('created_at', 'desc')])
}

export const getUserReports = async (userId: string): Promise<Report[]> => {
  return reportsService.getByField('user_id', userId, [orderBy('created_at', 'desc')])
}

export const getPublicTemplates = async (): Promise<Report[]> => {
  return reportsService.getByField('is_template', true, [orderBy('created_at', 'desc')])
}

export const getUserExternalConnections = async (userId: string): Promise<ExternalFirebaseConnection[]> => {
  return externalFirebaseConnectionsService.getByField('user_id', userId, [orderBy('created_at', 'desc')])
}

export const getConnectionSyncLogs = async (connectionId: string): Promise<SyncLog[]> => {
  return syncLogsService.getByField('connection_id', connectionId, [orderBy('started_at', 'desc'), limit(50)])
}

export const getUserSyncLogs = async (userId: string): Promise<SyncLog[]> => {
  return syncLogsService.getByField('user_id', userId, [orderBy('started_at', 'desc'), limit(100)])
}

export const getDatasetRecommendations = async (datasetId: string): Promise<ReportRecommendation[]> => {
  return reportRecommendationsService.getByField('dataset_id', datasetId, [orderBy('confidence_score', 'desc')])
}

export const getUserAuditLogs = async (userId: string, limitCount: number = 50): Promise<AuditLog[]> => {
  return auditLogsService.getByField('user_id', userId, [
    orderBy('created_at', 'desc'),
    limit(limitCount)
  ])
}

// Utility function to create audit log
export const createAuditLog = async (
  userId: string,
  action: string,
  resourceType: string,
  resourceId: string,
  details?: any,
  ipAddress?: string,
  userAgent?: string
): Promise<void> => {
  const auditLog: CreateAuditLog = {
    user_id: userId,
    action,
    resource_type: resourceType,
    resource_id: resourceId,
    details: details || null,
    ip_address: ipAddress || null,
    user_agent: userAgent || null,
  }
  
  await auditLogsService.create(auditLog)
}

export default {
  users: usersService,
  dataSources: dataSourcesService,
  datasets: datasetsService,
  reports: reportsService,
  reportRecommendations: reportRecommendationsService,
  auditLogs: auditLogsService,
  externalFirebaseConnections: externalFirebaseConnectionsService,
  syncLogs: syncLogsService,
  getUserDataSources,
  getDataSourceDatasets,
  getUserReports,
  getPublicTemplates,
  getDatasetRecommendations,
  getUserAuditLogs,
  createAuditLog,
  getUserExternalConnections,
  getConnectionSyncLogs,
  getUserSyncLogs,
}
