export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          role: 'admin' | 'user' | 'viewer'
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          role?: 'admin' | 'user' | 'viewer'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          role?: 'admin' | 'user' | 'viewer'
          created_at?: string
          updated_at?: string
        }
      }
      data_sources: {
        Row: {
          id: string
          user_id: string
          name: string
          type: 'csv' | 'google_sheets' | 'api'
          config: Json
          status: 'active' | 'inactive' | 'error'
          last_sync: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          type: 'csv' | 'google_sheets' | 'api'
          config: Json
          status?: 'active' | 'inactive' | 'error'
          last_sync?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          type?: 'csv' | 'google_sheets' | 'api'
          config?: Json
          status?: 'active' | 'inactive' | 'error'
          last_sync?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      datasets: {
        Row: {
          id: string
          data_source_id: string
          name: string
          schema: Json
          row_count: number
          file_size: number | null
          status: 'processing' | 'ready' | 'error'
          validation_results: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          data_source_id: string
          name: string
          schema: Json
          row_count?: number
          file_size?: number | null
          status?: 'processing' | 'ready' | 'error'
          validation_results?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          data_source_id?: string
          name?: string
          schema?: Json
          row_count?: number
          file_size?: number | null
          status?: 'processing' | 'ready' | 'error'
          validation_results?: Json | null
          created_at?: string
          updated_at?: string
        }
      }
      reports: {
        Row: {
          id: string
          user_id: string
          dataset_id: string
          name: string
          description: string | null
          type: string
          config: Json
          status: 'draft' | 'published' | 'archived'
          is_template: boolean
          template_category: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          dataset_id: string
          name: string
          description?: string | null
          type: string
          config: Json
          status?: 'draft' | 'published' | 'archived'
          is_template?: boolean
          template_category?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          dataset_id?: string
          name?: string
          description?: string | null
          type?: string
          config?: Json
          status?: 'draft' | 'published' | 'archived'
          is_template?: boolean
          template_category?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      report_recommendations: {
        Row: {
          id: string
          dataset_id: string
          report_type: string
          confidence_score: number
          reasoning: string
          suggested_config: Json
          created_at: string
        }
        Insert: {
          id?: string
          dataset_id: string
          report_type: string
          confidence_score: number
          reasoning: string
          suggested_config: Json
          created_at?: string
        }
        Update: {
          id?: string
          dataset_id?: string
          report_type?: string
          confidence_score?: number
          reasoning?: string
          suggested_config?: Json
          created_at?: string
        }
      }
      audit_logs: {
        Row: {
          id: string
          user_id: string
          action: string
          resource_type: string
          resource_id: string
          details: Json | null
          ip_address: string | null
          user_agent: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          action: string
          resource_type: string
          resource_id: string
          details?: Json | null
          ip_address?: string | null
          user_agent?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          action?: string
          resource_type?: string
          resource_id?: string
          details?: Json | null
          ip_address?: string | null
          user_agent?: string | null
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

// Firebase/Firestore specific types and utilities

// User types
export interface User {
  id: string
  email: string
  full_name?: string | null
  avatar_url?: string | null
  role: 'admin' | 'user' | 'viewer'
  created_at: string
  updated_at: string
}

export interface CreateUser {
  id: string
  email: string
  full_name?: string | null
  avatar_url?: string | null
  role?: 'admin' | 'user' | 'viewer'
  created_at?: string
  updated_at?: string
}

export interface UpdateUser {
  email?: string
  full_name?: string | null
  avatar_url?: string | null
  role?: 'admin' | 'user' | 'viewer'
  updated_at?: string
}

// Data Source types
export interface DataSource {
  id: string
  user_id: string
  name: string
  type: 'csv' | 'google_sheets' | 'api' | 'firebase_sync'
  config: Json
  status: 'active' | 'inactive' | 'error'
  last_sync?: string | null
  created_at: string
  updated_at: string
  metadata?: Json
}

export interface CreateDataSource {
  user_id: string
  name: string
  type: 'csv' | 'google_sheets' | 'api' | 'firebase_sync'
  config: Json
  status?: 'active' | 'inactive' | 'error'
  last_sync?: string | null
  created_at?: string
  updated_at?: string
  metadata?: Json
}

export interface UpdateDataSource {
  name?: string
  type?: 'csv' | 'google_sheets' | 'api' | 'firebase_sync'
  config?: Json
  status?: 'active' | 'inactive' | 'error'
  last_sync?: string | null
  updated_at?: string
  metadata?: Json
}

// Dataset types
export interface Dataset {
  id: string
  data_source_id: string
  name: string
  schema: Json
  row_count: number
  file_size?: number | null
  status: 'processing' | 'ready' | 'error'
  validation_results?: Json | null
  created_at: string
  updated_at: string
}

export interface CreateDataset {
  data_source_id: string
  name: string
  schema: Json
  row_count?: number
  file_size?: number | null
  status?: 'processing' | 'ready' | 'error'
  validation_results?: Json | null
  created_at?: string
  updated_at?: string
}

export interface UpdateDataset {
  name?: string
  schema?: Json
  row_count?: number
  file_size?: number | null
  status?: 'processing' | 'ready' | 'error'
  validation_results?: Json | null
  updated_at?: string
}

// Report types
export interface Report {
  id: string
  user_id: string
  dataset_id: string
  name: string
  description?: string | null
  type: string
  config: Json
  status: 'draft' | 'published' | 'archived'
  is_template: boolean
  template_category?: string | null
  created_at: string
  updated_at: string
}

export interface CreateReport {
  user_id: string
  dataset_id: string
  name: string
  description?: string | null
  type: string
  config: Json
  status?: 'draft' | 'published' | 'archived'
  is_template?: boolean
  template_category?: string | null
  created_at?: string
  updated_at?: string
}

export interface UpdateReport {
  name?: string
  description?: string | null
  type?: string
  config?: Json
  status?: 'draft' | 'published' | 'archived'
  is_template?: boolean
  template_category?: string | null
  updated_at?: string
}

// Report Recommendation types
export interface ReportRecommendation {
  id: string
  dataset_id: string
  report_type: string
  confidence_score: number
  reasoning: string
  suggested_config: Json
  created_at: string
}

export interface CreateReportRecommendation {
  dataset_id: string
  report_type: string
  confidence_score: number
  reasoning: string
  suggested_config: Json
  created_at?: string
}

// Audit Log types
export interface AuditLog {
  id: string
  user_id: string
  action: string
  resource_type: string
  resource_id: string
  details?: Json | null
  ip_address?: string | null
  user_agent?: string | null
  created_at: string
}

export interface CreateAuditLog {
  user_id: string
  action: string
  resource_type: string
  resource_id: string
  details?: Json | null
  ip_address?: string | null
  user_agent?: string | null
  created_at?: string
}

// External Firebase Connection types
export interface ExternalFirebaseConnection {
  id: string
  user_id: string
  name: string
  description?: string
  project_id: string
  config: {
    apiKey: string
    authDomain: string
    projectId: string
    storageBucket: string
    messagingSenderId: string
    appId: string
    measurementId?: string
  }
  status: 'active' | 'inactive' | 'error' | 'testing'
  last_sync?: string | null
  sync_settings: {
    enabled: boolean
    collections: string[]
    sync_direction: 'pull' | 'push' | 'bidirectional'
    conflict_resolution: 'source_wins' | 'target_wins' | 'merge' | 'manual'
    sync_frequency: 'realtime' | 'hourly' | 'daily' | 'manual'
  }
  created_at: string
  updated_at: string
}

export interface CreateExternalFirebaseConnection {
  user_id: string
  name: string
  description?: string
  project_id: string
  config: {
    apiKey: string
    authDomain: string
    projectId: string
    storageBucket: string
    messagingSenderId: string
    appId: string
    measurementId?: string
  }
  status?: 'active' | 'inactive' | 'error' | 'testing'
  sync_settings?: {
    enabled?: boolean
    collections?: string[]
    sync_direction?: 'pull' | 'push' | 'bidirectional'
    conflict_resolution?: 'source_wins' | 'target_wins' | 'merge' | 'manual'
    sync_frequency?: 'realtime' | 'hourly' | 'daily' | 'manual'
  }
  created_at?: string
  updated_at?: string
}

export interface UpdateExternalFirebaseConnection {
  name?: string
  description?: string
  config?: {
    apiKey?: string
    authDomain?: string
    projectId?: string
    storageBucket?: string
    messagingSenderId?: string
    appId?: string
    measurementId?: string
  }
  status?: 'active' | 'inactive' | 'error' | 'testing'
  last_sync?: string | null
  sync_settings?: {
    enabled?: boolean
    collections?: string[]
    sync_direction?: 'pull' | 'push' | 'bidirectional'
    conflict_resolution?: 'source_wins' | 'target_wins' | 'merge' | 'manual'
    sync_frequency?: 'realtime' | 'hourly' | 'daily' | 'manual'
  }
  updated_at?: string
}

// Sync Log types
export interface SyncLog {
  id: string
  connection_id: string
  user_id: string
  sync_type: 'pull' | 'push' | 'bidirectional'
  status: 'started' | 'completed' | 'failed' | 'partial'
  collections_synced: string[]
  records_processed: number
  records_success: number
  records_failed: number
  error_message?: string | null
  sync_duration_ms: number
  started_at: string
  completed_at?: string | null
  details?: Json | null
}

export interface CreateSyncLog {
  connection_id: string
  user_id: string
  sync_type: 'pull' | 'push' | 'bidirectional'
  status?: 'started' | 'completed' | 'failed' | 'partial'
  collections_synced?: string[]
  records_processed?: number
  records_success?: number
  records_failed?: number
  error_message?: string | null
  sync_duration_ms?: number
  started_at?: string
  completed_at?: string | null
  details?: Json | null
}

// Collection names for Firestore
export const COLLECTIONS = {
  USERS: 'users',
  DATA_SOURCES: 'data_sources',
  DATASETS: 'datasets',
  REPORTS: 'reports',
  REPORT_RECOMMENDATIONS: 'report_recommendations',
  AUDIT_LOGS: 'audit_logs',
  EXTERNAL_FIREBASE_CONNECTIONS: 'external_firebase_connections',
  SYNC_LOGS: 'sync_logs',
} as const
