import { analyzeDataQuality, applyDataCleaningSuggestion } from '../data-analysis'

describe('Data Quality Analysis', () => {
  const sampleData = [
    { name: '<PERSON>', age: 25, salary: 50000, department: 'Engineering' },
    { name: '<PERSON>', age: null, salary: 60000, department: 'Marketing' },
    { name: '', age: 30, salary: 'invalid', department: 'Sales' },
    { name: '<PERSON>', age: 35, salary: 70000, department: 'Engineering' },
    { name: '<PERSON>', age: 28, salary: 55000, department: null }
  ]

  const sampleSchema = {
    name: 'string',
    age: 'number',
    salary: 'number',
    department: 'string'
  }

  test('should identify missing values', () => {
    const report = analyzeDataQuality(sampleData, sampleSchema)
    
    expect(report.issues.length).toBeGreaterThan(0)
    
    // Should find missing values in age column
    const ageIssue = report.issues.find(issue => 
      issue.type === 'missing_values' && issue.column === 'age'
    )
    expect(ageIssue).toBeDefined()
    expect(ageIssue?.affectedRows).toBe(1)
    
    // Should find missing values in name column
    const nameIssue = report.issues.find(issue => 
      issue.type === 'missing_values' && issue.column === 'name'
    )
    expect(nameIssue).toBeDefined()
    expect(nameIssue?.affectedRows).toBe(1)
  })

  test('should identify type mismatches', () => {
    const report = analyzeDataQuality(sampleData, sampleSchema)
    
    // Should find type mismatch in salary column
    const salaryIssue = report.issues.find(issue => 
      issue.type === 'type_mismatch' && issue.column === 'salary'
    )
    expect(salaryIssue).toBeDefined()
    expect(salaryIssue?.affectedRows).toBe(1)
  })

  test('should generate appropriate suggestions', () => {
    const report = analyzeDataQuality(sampleData, sampleSchema)
    
    expect(report.suggestions.length).toBeGreaterThan(0)
    
    // Should suggest filling missing numeric values with mean
    const fillMeanSuggestion = report.suggestions.find(suggestion => 
      suggestion.type === 'fill_missing' && 
      suggestion.parameters?.method === 'mean'
    )
    expect(fillMeanSuggestion).toBeDefined()
    
    // Should suggest converting type mismatches
    const convertTypeSuggestion = report.suggestions.find(suggestion => 
      suggestion.type === 'convert_type'
    )
    expect(convertTypeSuggestion).toBeDefined()
  })

  test('should apply fill missing suggestion correctly', () => {
    const report = analyzeDataQuality(sampleData, sampleSchema)
    
    const fillMeanSuggestion = report.suggestions.find(suggestion => 
      suggestion.type === 'fill_missing' && 
      suggestion.parameters?.method === 'mean' &&
      suggestion.parameters?.column === 'age'
    )
    
    if (fillMeanSuggestion) {
      const result = applyDataCleaningSuggestion(sampleData, sampleSchema, fillMeanSuggestion)
      
      // Should have filled the missing age value
      const rowWithMissingAge = result.data.find(row => row.name === 'Jane Smith')
      expect(rowWithMissingAge?.age).not.toBeNull()
      expect(typeof rowWithMissingAge?.age).toBe('number')
    }
  })

  test('should handle empty dataset', () => {
    const report = analyzeDataQuality([], {})
    
    expect(report.issues.length).toBe(0)
    expect(report.suggestions.length).toBe(0)
    expect(report.summary.totalIssues).toBe(0)
  })

  test('should calculate summary statistics correctly', () => {
    const report = analyzeDataQuality(sampleData, sampleSchema)
    
    expect(report.summary.totalIssues).toBeGreaterThan(0)
    expect(report.summary.affectedColumns.length).toBeGreaterThan(0)
    expect(report.summary.affectedRows).toBeGreaterThan(0)
  })
})
